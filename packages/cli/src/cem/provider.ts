import type { Component } from '@wc-toolkit/cem-utilities';
import { getAllComponents, getComponentByClassName, getComponentByTagName } from '@wc-toolkit/cem-utilities';
import type { Package } from 'custom-elements-manifest';
import { glob } from 'glob';
import * as fs from 'fs/promises';
import * as path from 'path';
import type { ComponentDetailLevel, SearchMatchingMode } from '@wcai/shared';
import { filterComponents, getComponentDetails } from '@wcai/shared';
import { Logger } from '../system/logger';

export interface ParsedPackageFile {
  name: string;
  customElements?: string;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

export interface ManifestSource {
  path: string;
  packageJsonPath?: string;
  dependencyName?: string;
  isLocal: boolean;
}

export class CemProvider {
  private manifests: Package[] = [];
  private components: Component[] = [];
  private manifestSources: Map<string, ManifestSource[]> = new Map();
  private manifestCache: Map<string, { manifest: Package; lastModified: number }> = new Map();
  private componentCache: Map<string, Component> = new Map(); // tagName -> Component
  private classCache: Map<string, Component> = new Map(); // className -> Component
  private searchCache: Map<string, Component[]> = new Map(); // query:matching -> results
  private lastScan = 0;
  private readonly scanInterval = 5000; // 5 seconds

  constructor(private workspace: string) {}

  async initialize(): Promise<void> {
    await this.scanManifests();
  }

  private async scanManifests(): Promise<void> {
    const now = Date.now();
    if (now - this.lastScan < this.scanInterval && !(await this.shouldRescan())) {
      return; // Skip if scanned recently and no files changed
    }

    try {
      Logger.log(`Scanning for custom-elements.json files in ${this.workspace}`);

      // Clear previous manifest sources
      this.manifestSources.clear();

      const manifestPathsSet = new Set<string>();

      // Package-first approach: Find all package.json files and extract manifests
      const localPackages = await this.findLocalPackages();
      for (const packageJsonPath of localPackages) {
        const manifests = await this.findManifestsFromPackage(packageJsonPath, {
          includeDependencies: true,
        });
        manifests.forEach(m => manifestPathsSet.add(m));
      }

      // Fallback: If no manifests found via package.json, search directly for custom-elements.json files
      if (manifestPathsSet.size === 0) {
        const localManifests = await this.findLocalManifests();
        localManifests.forEach(m => manifestPathsSet.add(m));
      }

      const allPaths = Array.from(manifestPathsSet);
      Logger.log(`Found ${allPaths.length} manifest files`);

      // Load and parse manifests with caching
      const manifests: Package[] = [];
      for (const manifestPath of allPaths) {
        try {
          const manifest = await this.loadManifestWithCache(manifestPath);
          if (manifest) {
            manifests.push(manifest);
            Logger.log(`Loaded manifest: ${manifestPath}`);
          }
        } catch (error) {
          Logger.error(`Failed to load manifest ${manifestPath}:`, error);
        }
      }

      this.manifests = manifests;

      // Extract all components and populate caches
      this.components = [];
      this.clearComponentCaches(); // Clear old caches

      for (const manifest of manifests) {
        const components = getAllComponents(manifest);
        this.components.push(...components);

        // Populate component caches
        components.forEach(component => {
          if (component.tagName) {
            this.componentCache.set(component.tagName, component);
          }
          if (typeof component.className === 'string') {
            this.classCache.set(component.className, component);
          }
        });
      }

      Logger.log(`Loaded ${this.components.length} components from ${manifests.length} manifests`);
      this.lastScan = now;
    } catch (error) {
      Logger.error('Failed to scan manifests:', error);
    }
  }

  async getAllComponents(detail: ComponentDetailLevel = 'basic'): Promise<any[]> {
    await this.scanManifests();
    return this.components.map(component => getComponentDetails(component, detail));
  }

  async searchComponents(query: string, matching: SearchMatchingMode = 'any'): Promise<any[]> {
    await this.scanManifests();

    // Create cache key
    const cacheKey = `${query.trim()}:${matching}`;

    // Check cache first
    if (this.searchCache.has(cacheKey)) {
      return this.searchCache.get(cacheKey)!.map(component => getComponentDetails(component, 'public'));
    }

    // Perform search
    const filtered = filterComponents(this.components, query, matching);

    // Cache the results
    this.searchCache.set(cacheKey, filtered);

    return filtered.map(component => getComponentDetails(component, 'public'));
  }

  async getComponentDetails(identifier: string, detail: ComponentDetailLevel = 'public'): Promise<any | null> {
    await this.scanManifests();

    // Try to find by tag name first (use cache)
    if (this.componentCache.has(identifier)) {
      return getComponentDetails(this.componentCache.get(identifier)!, detail);
    }

    // Try to find by class name (use cache)
    if (this.classCache.has(identifier)) {
      return getComponentDetails(this.classCache.get(identifier)!, detail);
    }

    // Fallback to searching manifests directly (for edge cases)
    for (const manifest of this.manifests) {
      const component = getComponentByTagName(manifest, identifier) || getComponentByClassName(manifest, identifier);
      if (component) {
        return getComponentDetails(component, detail);
      }
    }

    return null;
  }

  getStats() {
    return {
      manifestCount: this.manifests.length,
      componentCount: this.components.length,
      lastScan: this.lastScan,
    };
  }

  getManifestSources(manifestPath: string): ManifestSource[] | undefined {
    return this.manifestSources.get(manifestPath);
  }

  getAllManifestSources(): Map<string, ManifestSource[]> {
    return new Map(this.manifestSources);
  }

  private addManifestSource(manifestPath: string, source: ManifestSource): void {
    const sources = this.manifestSources.get(manifestPath) || [];

    // Check if this exact source already exists
    const exists = sources.some(
      s =>
        s.isLocal === source.isLocal &&
        s.packageJsonPath === source.packageJsonPath &&
        s.dependencyName === source.dependencyName,
    );

    if (!exists) {
      sources.push(source);
      this.manifestSources.set(manifestPath, sources);
    }
  }

  private async findLocalPackages(): Promise<string[]> {
    const localPackages: string[] = [];

    try {
      const files = await glob('**/package.json', {
        cwd: this.workspace,
        ignore: ['**/node_modules/**/package.json'],
        absolute: true,
      });
      localPackages.push(...files);
    } catch (error) {
      Logger.error('Failed to find local packages:', error);
    }

    return localPackages;
  }

  private async findManifestsFromPackage(
    packageJsonPath: string,
    options?: { isLocal?: boolean; includeDependencies?: boolean },
  ): Promise<string[]> {
    const manifests: string[] = [];

    try {
      const packageContent = await fs.readFile(packageJsonPath, 'utf-8');
      const packageData = JSON.parse(packageContent) as ParsedPackageFile;

      if (packageData.customElements) {
        const manifestPath = path.resolve(path.dirname(packageJsonPath), packageData.customElements);
        manifests.push(manifestPath);

        // Track source information for package manifest
        this.addManifestSource(manifestPath, {
          path: manifestPath,
          packageJsonPath,
          dependencyName: packageData.name,
          isLocal: options?.isLocal ?? true,
        });
      }

      if (options?.includeDependencies) {
        const dependencies = {
          ...(packageData.dependencies || {}),
          ...(packageData.devDependencies || {}),
        };

        for (const dep of Object.keys(dependencies)) {
          const depPackageJsonPath = path.join(path.dirname(packageJsonPath), 'node_modules', dep, 'package.json');
          try {
            const depManifests = await this.findManifestsFromPackage(depPackageJsonPath, {
              isLocal: false,
              includeDependencies: false,
            });

            if (depManifests.length > 0) {
              manifests.push(...depManifests);
            }
          } catch (error) {
            // Dependency might not be installed, continue silently
            Logger.log(`Dependency ${dep} not found or invalid package.json`);
          }
        }
      }
    } catch (error) {
      Logger.error(`Failed to process package.json ${packageJsonPath}:`, error);
    }

    return manifests;
  }

  private async findLocalManifests(): Promise<string[]> {
    const localManifests: string[] = [];
    try {
      const files = await glob('**/custom-elements.json', {
        cwd: this.workspace,
        ignore: ['**/node_modules/**', '**/dist/**', '**/build/**'],
        absolute: true,
      });

      for (const manifestPath of files) {
        localManifests.push(manifestPath);

        // Track source information for local manifests
        this.addManifestSource(manifestPath, {
          path: manifestPath,
          isLocal: true,
        });
      }

      // Also check node_modules for dependencies
      const nodeModulesFiles = await glob('node_modules/*/custom-elements.json', {
        cwd: this.workspace,
        absolute: true,
      });

      for (const manifestPath of nodeModulesFiles) {
        localManifests.push(manifestPath);

        // Track source information for dependency manifests
        this.addManifestSource(manifestPath, {
          path: manifestPath,
          isLocal: false,
        });
      }
    } catch (error) {
      Logger.error('Failed to find local manifests:', error);
    }

    return localManifests;
  }

  /**
   * Check if we should rescan based on file modification times
   */
  private async shouldRescan(): Promise<boolean> {
    try {
      // Check if any cached manifest files have been modified
      for (const [manifestPath, cached] of this.manifestCache) {
        try {
          const stat = await fs.stat(manifestPath);
          if (stat.mtimeMs !== cached.lastModified) {
            Logger.log(`Manifest file changed: ${manifestPath}`);
            return true;
          }
        } catch (error) {
          // File might have been deleted
          Logger.log(`Manifest file no longer exists: ${manifestPath}`);
          return true;
        }
      }

      // For now, we don't check package.json files for changes
      // This could be added in the future for even more granular caching
      return false;
    } catch (error) {
      Logger.error('Error checking if rescan needed:', error);
      return true; // When in doubt, rescan
    }
  }

  /**
   * Load a manifest with file modification time caching
   */
  private async loadManifestWithCache(manifestPath: string): Promise<Package | null> {
    try {
      const stat = await fs.stat(manifestPath);
      const currentModified = stat.mtimeMs;

      // Check if we have a cached version that's still valid
      const cached = this.manifestCache.get(manifestPath);
      if (cached && cached.lastModified === currentModified) {
        return cached.manifest;
      }

      // Load and parse the manifest
      const content = await fs.readFile(manifestPath, 'utf-8');
      const manifest = JSON.parse(content) as Package;

      // Cache the manifest with its modification time
      this.manifestCache.set(manifestPath, {
        manifest,
        lastModified: currentModified,
      });

      return manifest;
    } catch (error) {
      Logger.error(`Failed to load manifest ${manifestPath}:`, error);
      return null;
    }
  }

  /**
   * Clear component-related caches
   */
  private clearComponentCaches(): void {
    this.componentCache.clear();
    this.classCache.clear();
    this.searchCache.clear();
  }

  /**
   * Clear all caches (useful for debugging or memory management)
   */
  clearAllCaches(): void {
    this.manifestCache.clear();
    this.clearComponentCaches();
    this.manifestSources.clear();
    this.manifests = [];
    this.components = [];
    this.lastScan = 0;
  }

  /**
   * Get detailed cache statistics for debugging and monitoring
   */
  getCacheStats() {
    return {
      manifestCount: this.manifests.length,
      componentCount: this.components.length,
      lastScan: this.lastScan,
      cacheStats: {
        manifestCacheSize: this.manifestCache.size,
        componentCacheSize: this.componentCache.size,
        classCacheSize: this.classCache.size,
        searchCacheSize: this.searchCache.size,
      },
    };
  }
}
