import type { CliOptions } from '@wcai/shared';
import { createHttpTransport } from '../transports/http';
import { setupMcpServer } from '../mcp/setup';
import { Logger } from '../system/logger';
import { CemProvider } from '../cem/provider';

export class ServeCommand {
  async execute(options: CliOptions): Promise<void> {
    try {
      Logger.configure(options.verbose ? 'debug' : 'warn');
      Logger.log('Starting MCP server with HTTP/SSE transport');
      Logger.log(`Workspace: ${options.workspace}`);
      Logger.log(`Port: ${options.port || 'auto'}`);
      Logger.log(`Host: ${options.host || '127.0.0.1'}`);

      // Initialize CEM provider
      const cemProvider = new CemProvider(options.workspace!);
      await cemProvider.initialize();

      // Create HTTP transport with MCP server
      const transportInfo = await createHttpTransport(
        options.port ? parseInt(options.port.toString()) : 0,
        options.host || '127.0.0.1',
        mcpServer => {
          setupMcpServer(mcpServer, cemProvider);
        },
        { mcp: { name: 'wcai-mcp-server', version: '0.0.3' } },
      );

      Logger.log(`MCP server started at ${transportInfo.url}`);
      Logger.log(`MCP endpoint: ${transportInfo.mcpUrl}`);
      Logger.log(`SSE endpoint: ${transportInfo.sseUrl}`);

      // Output server info for programmatic use
      console.log(
        JSON.stringify({
          transport: 'http',
          url: transportInfo.url,
          mcpUrl: transportInfo.mcpUrl,
          sseUrl: transportInfo.sseUrl,
          port: transportInfo.port,
          host: transportInfo.hostName,
          pid: process.pid,
          workspace: options.workspace,
        }),
      );

      // Keep the process running
      process.on('SIGINT', () => {
        Logger.log('Shutting down MCP server');
        transportInfo.httpServer.close(() => {
          process.exit(0);
        });
      });

      process.on('SIGTERM', () => {
        Logger.log('Shutting down MCP server');
        transportInfo.httpServer.close(() => {
          process.exit(0);
        });
      });
    } catch (error) {
      Logger.error('Failed to start HTTP MCP server', error);
      process.exit(1);
    }
  }
}
