import type { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { ResourceTemplate } from '@modelcontextprotocol/sdk/server/mcp.js';
import type { CemProvider } from '../cem/provider';

/**
 * Configure MCP server with all tools and resources
 * This centralizes the MCP setup to avoid duplication across transport commands
 */
export function setupMcpServer(mcpServer: McpServer, cemProvider: CemProvider): void {
  // Add resources
  mcpServer.resource('manifest://components', 'manifest://components', async () => {
    const components = await cemProvider.getAllComponents();
    return {
      contents: [
        {
          uri: 'manifest://components',
          text: JSON.stringify(components, null, 2),
          mimeType: 'application/json',
        },
      ],
    };
  });

  // Add dynamic resource for specific components
  mcpServer.resource(
    'manifest-components',
    new ResourceTemplate('manifest://components/{tag}', { list: undefined }),
    async (uri: URL, variables) => {
      const tag = variables.tag as string;
      if (!tag) {
        throw new Error('Component tag is required');
      }

      const component = await cemProvider.getComponentDetails(tag, 'all');
      if (!component) {
        throw new Error(`Component with tag '${tag}' not found`);
      }

      return {
        contents: [
          {
            uri: uri.href,
            text: JSON.stringify(component, null, 2),
            mimeType: 'application/json',
          },
        ],
      };
    },
  );

  // Add tools
  mcpServer.tool(
    'search-components',
    'Search for web components by name, tag, or description',
    {
      query: {
        type: 'string',
        description: 'Search query for component name, tag, or description',
      },
      matching: {
        type: 'string',
        enum: ['strict', 'all', 'any'],
        description: 'Matching mode for search',
        default: 'any',
      },
    },
    async args => {
      const { query, matching = 'any' } = args as { query: string; matching?: 'strict' | 'all' | 'any' };
      const components = await cemProvider.searchComponents(query, matching);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(components, null, 2),
          },
        ],
      };
    },
  );

  mcpServer.tool(
    'get-component-details-by-tag-name',
    'Get detailed information about a component by its tag name',
    {
      tagName: {
        type: 'string',
        description: 'Component tag name (e.g., "my-button")',
      },
      detail: {
        type: 'string',
        enum: ['basic', 'public', 'all'],
        description: 'Level of detail to return',
        default: 'public',
      },
    },
    async args => {
      const { tagName, detail = 'public' } = args as {
        tagName: string;
        detail?: 'basic' | 'public' | 'all';
      };
      const component = await cemProvider.getComponentDetails(tagName, detail);
      return {
        content: [
          {
            type: 'text',
            text: component ? JSON.stringify(component, null, 2) : 'Component not found',
          },
        ],
      };
    },
  );

  mcpServer.tool(
    'get-component-details-by-class-name',
    'Get detailed information about a component by its class name',
    {
      className: {
        type: 'string',
        description: 'Component class name (e.g., "MyButton")',
      },
      detail: {
        type: 'string',
        enum: ['basic', 'public', 'all'],
        description: 'Level of detail to return',
        default: 'public',
      },
    },
    async args => {
      const { className, detail = 'public' } = args as {
        className: string;
        detail?: 'basic' | 'public' | 'all';
      };
      const component = await cemProvider.getComponentDetails(className, detail);
      return {
        content: [
          {
            type: 'text',
            text: component ? JSON.stringify(component, null, 2) : 'Component not found',
          },
        ],
      };
    },
  );

  mcpServer.tool(
    'list-all-components',
    'List all available web components in the workspace',
    {
      detail: {
        type: 'string',
        enum: ['basic', 'public', 'all'],
        description: 'Level of detail to return',
        default: 'basic',
      },
    },
    async args => {
      const { detail = 'basic' } = args as { detail?: 'basic' | 'public' | 'all' };
      const components = await cemProvider.getAllComponents(detail);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(components, null, 2),
          },
        ],
      };
    },
  );
}
