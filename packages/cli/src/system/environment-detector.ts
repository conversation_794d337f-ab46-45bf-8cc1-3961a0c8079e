import * as fs from 'fs/promises';
import * as path from 'path';
import { Logger } from './logger';

export interface EnvironmentInfo {
  projectType: 'lit' | 'stencil' | 'angular' | 'vue' | 'react' | 'vanilla' | 'unknown';
  buildTool: 'vite' | 'webpack' | 'rollup' | 'parcel' | 'esbuild' | 'unknown';
  packageManager: 'npm' | 'yarn' | 'pnpm' | 'bun' | 'unknown';
  hasManifests: boolean;
  manifestPaths: string[];
  suggestedConfig: {
    searchPaths: string[];
    port: number;
    transport: 'stdio' | 'http' | 'auto';
  };
}

export class EnvironmentDetector {
  async detect(workspaceDir: string): Promise<EnvironmentInfo> {
    Logger.log('🔍 Detecting project environment...');

    const [
      projectType,
      buildTool,
      packageManager,
      manifestInfo,
    ] = await Promise.all([
      this.detectProjectType(workspaceDir),
      this.detectBuildTool(workspaceDir),
      this.detectPackageManager(workspaceDir),
      this.detectManifests(workspaceDir),
    ]);

    const suggestedConfig = this.generateSuggestedConfig({
      projectType,
      buildTool,
      packageManager,
      hasManifests: manifestInfo.hasManifests,
      manifestPaths: manifestInfo.manifestPaths,
    });

    return {
      projectType,
      buildTool,
      packageManager,
      hasManifests: manifestInfo.hasManifests,
      manifestPaths: manifestInfo.manifestPaths,
      suggestedConfig,
    };
  }

  private async detectProjectType(workspaceDir: string): Promise<EnvironmentInfo['projectType']> {
    try {
      const packageJsonPath = path.join(workspaceDir, 'package.json');
      const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
      
      const deps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies,
      };

      // Check for specific frameworks
      if (deps.lit || deps['@lit/reactive-element'] || deps['lit-element']) {
        return 'lit';
      }
      
      if (deps['@stencil/core']) {
        return 'stencil';
      }
      
      if (deps['@angular/core']) {
        return 'angular';
      }
      
      if (deps.vue || deps['@vue/core']) {
        return 'vue';
      }
      
      if (deps.react || deps['react-dom']) {
        return 'react';
      }

      // Check for web component related packages
      if (deps['@webcomponents/webcomponentsjs'] || 
          deps['custom-elements-manifest'] ||
          Object.keys(deps).some(dep => dep.includes('web-component'))) {
        return 'vanilla';
      }

      return 'unknown';
    } catch {
      return 'unknown';
    }
  }

  private async detectBuildTool(workspaceDir: string): Promise<EnvironmentInfo['buildTool']> {
    const configFiles = [
      { file: 'vite.config.js', tool: 'vite' as const },
      { file: 'vite.config.ts', tool: 'vite' as const },
      { file: 'webpack.config.js', tool: 'webpack' as const },
      { file: 'webpack.config.ts', tool: 'webpack' as const },
      { file: 'rollup.config.js', tool: 'rollup' as const },
      { file: 'rollup.config.ts', tool: 'rollup' as const },
      { file: 'parcel.config.js', tool: 'parcel' as const },
      { file: 'esbuild.config.js', tool: 'esbuild' as const },
    ];

    for (const { file, tool } of configFiles) {
      try {
        await fs.access(path.join(workspaceDir, file));
        return tool;
      } catch {
        // Continue checking
      }
    }

    return 'unknown';
  }

  private async detectPackageManager(workspaceDir: string): Promise<EnvironmentInfo['packageManager']> {
    const lockFiles = [
      { file: 'pnpm-lock.yaml', manager: 'pnpm' as const },
      { file: 'yarn.lock', manager: 'yarn' as const },
      { file: 'bun.lockb', manager: 'bun' as const },
      { file: 'package-lock.json', manager: 'npm' as const },
    ];

    for (const { file, manager } of lockFiles) {
      try {
        await fs.access(path.join(workspaceDir, file));
        return manager;
      } catch {
        // Continue checking
      }
    }

    return 'unknown';
  }

  private async detectManifests(workspaceDir: string): Promise<{
    hasManifests: boolean;
    manifestPaths: string[];
  }> {
    try {
      const { glob } = await import('glob');
      const manifestPaths = await glob('**/custom-elements.json', {
        cwd: workspaceDir,
        ignore: ['**/node_modules/**'],
      });

      return {
        hasManifests: manifestPaths.length > 0,
        manifestPaths,
      };
    } catch {
      return {
        hasManifests: false,
        manifestPaths: [],
      };
    }
  }

  private generateSuggestedConfig(info: Partial<EnvironmentInfo>): EnvironmentInfo['suggestedConfig'] {
    const config = {
      searchPaths: ['./src'],
      port: 3000,
      transport: 'auto' as const,
    };

    // Adjust search paths based on project type
    switch (info.projectType) {
      case 'lit':
        config.searchPaths = ['./src', './packages/*/src'];
        break;
      case 'stencil':
        config.searchPaths = ['./src/components', './dist'];
        break;
      case 'angular':
        config.searchPaths = ['./src/app', './projects'];
        break;
      case 'vue':
        config.searchPaths = ['./src/components'];
        break;
      case 'react':
        config.searchPaths = ['./src/components'];
        break;
    }

    // Adjust port based on build tool (avoid conflicts)
    switch (info.buildTool) {
      case 'vite':
        config.port = 3001; // Vite uses 3000 by default
        break;
      case 'webpack':
        config.port = 3002; // Webpack dev server often uses 3000
        break;
      case 'parcel':
        config.port = 3003; // Parcel uses 1234 by default, but be safe
        break;
    }

    // If manifests exist, prefer STDIO for better AI integration
    if (info.hasManifests) {
      config.transport = 'stdio';
    }

    return config;
  }

  async generateSmartConfig(workspaceDir: string): Promise<any> {
    const envInfo = await this.detect(workspaceDir);
    
    Logger.log(`Detected: ${envInfo.projectType} project with ${envInfo.buildTool} build tool`);
    Logger.log(`Package manager: ${envInfo.packageManager}`);
    Logger.log(`Manifests found: ${envInfo.hasManifests ? 'Yes' : 'No'}`);

    return {
      // Auto-detected workspace
      workspace: workspaceDir,
      
      // Smart transport selection
      transport: envInfo.suggestedConfig.transport,
      
      // HTTP config with smart port selection
      http: {
        port: envInfo.suggestedConfig.port,
        host: '127.0.0.1',
      },
      
      // STDIO config
      stdio: {
        timeout: 5000,
      },
      
      // Smart logging based on project type
      logging: {
        level: envInfo.projectType === 'unknown' ? 'debug' : 'info',
      },
      
      // Smart manifest search paths
      manifests: {
        searchPaths: envInfo.suggestedConfig.searchPaths,
        include: ['**/custom-elements.json'],
        exclude: [
          '**/node_modules/**',
          '**/dist/**',
          '**/build/**',
          '**/.next/**',
          '**/coverage/**',
        ],
      },
      
      // Add project-specific metadata
      _detected: {
        projectType: envInfo.projectType,
        buildTool: envInfo.buildTool,
        packageManager: envInfo.packageManager,
        manifestsFound: envInfo.manifestPaths.length,
      },
    };
  }
}
