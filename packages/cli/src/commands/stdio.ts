import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import type { CliOptions } from '@wcai/shared';
import { createMcpServer } from '../mcp/server';
import { setupMcpServer } from '../mcp/setup';
import { Logger } from '../system/logger';
import { CemProvider } from '../cem/provider';

export class StdioCommand {
  async execute(options: CliOptions): Promise<void> {
    try {
      Logger.configure(options.verbose ? 'debug' : 'warn');
      Logger.log('Starting MCP server with STDIO transport');
      Logger.log(`Workspace: ${options.workspace}`);

      // Initialize CEM provider
      const cemProvider = new CemProvider(options.workspace!);
      await cemProvider.initialize();

      // Create MCP server with CEM provider
      const server = createMcpServer('wcai-mcp-server', '0.0.3', mcpServer => {
        setupMcpServer(mcpServer, cemProvider);
      });

      // Create STDIO transport
      const transport = new StdioServerTransport();

      // Connect server to transport
      await server.connect(transport);

      Logger.log('MCP server started with STDIO transport');

      // Keep the process running
      process.on('SIGINT', () => {
        Logger.log('Shutting down MCP server');
        process.exit(0);
      });

      process.on('SIGTERM', () => {
        Logger.log('Shutting down MCP server');
        process.exit(0);
      });
    } catch (error) {
      Logger.error('Failed to start STDIO MCP server', error);
      process.exit(1);
    }
  }
}
