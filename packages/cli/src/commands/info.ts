import * as fs from 'fs/promises';
import * as path from 'path';
import type { CliOptions } from '@wcai/shared';
import { Logger } from '../system/logger';
import { CemProvider } from '../cem/provider';
import { ConfigLoader } from '../config/config-loader';

interface InfoOptions extends CliOptions {
  components?: boolean;
  config?: boolean;
  manifests?: boolean;
  all?: boolean;
}

export class InfoCommand {
  async execute(options: InfoOptions): Promise<void> {
    try {
      Logger.configure(options.verbose ? 'debug' : 'info');

      const workspaceDir = options.workspace || process.cwd();

      Logger.log('📊 Web Component AI Tools - System Information\n');
      Logger.log(`Workspace: ${workspaceDir}`);
      Logger.log(`Node.js: ${process.version}`);
      Logger.log(`Platform: ${process.platform} ${process.arch}\n`);

      // Show all info by default, or specific sections
      const showAll = options.all || (!options.components && !options.config && !options.manifests);

      if (showAll || options.config) {
        await this.showConfigInfo(workspaceDir, options);
      }

      if (showAll || options.manifests) {
        await this.showManifestInfo(workspaceDir, options);
      }

      if (showAll || options.components) {
        await this.showComponentInfo(workspaceDir, options);
      }
    } catch (error) {
      Logger.error('Failed to gather system information', error);
      process.exit(1);
    }
  }

  private async showConfigInfo(workspaceDir: string, options: InfoOptions): Promise<void> {
    Logger.log('⚙️  Configuration:');

    try {
      const configLoader = new ConfigLoader();
      const config = await configLoader.loadConfig(workspaceDir);

      Logger.log(`  Transport: ${config.transport}`);
      if (config.http) {
        Logger.log(`  HTTP Port: ${config.http.port || 'auto'}`);
        Logger.log(`  HTTP Host: ${config.http.host}`);
      }
      if (config.stdio) {
        Logger.log(`  STDIO Timeout: ${config.stdio.timeout}ms`);
      }
      if (config.logging) {
        Logger.log(`  Log Level: ${config.logging.level}`);
        if (config.logging.file) {
          Logger.log(`  Log File: ${config.logging.file}`);
        }
      }

      // Check for config file
      const configFiles = ['.wcairc.json', '.wcairc.js', 'wcai.config.json', 'wcai.config.js'];
      let configFileFound = false;

      for (const filename of configFiles) {
        try {
          await fs.access(path.join(workspaceDir, filename));
          Logger.log(`  Config File: ${filename} ✅`);
          configFileFound = true;
          break;
        } catch {
          // Continue checking
        }
      }

      if (!configFileFound) {
        Logger.log('  Config File: None (using defaults) ⚠️');
        Logger.log('    💡 Run "wcai-mcp init" to create a configuration file');
      }
    } catch (error) {
      Logger.log('  ❌ Error loading configuration');
      if (options.verbose) {
        Logger.error('Config error details:', error);
      }
    }

    Logger.log('');
  }

  private async showManifestInfo(workspaceDir: string, options: InfoOptions): Promise<void> {
    Logger.log('📄 Custom Elements Manifests:');

    try {
      const cemProvider = new CemProvider(workspaceDir);
      await cemProvider.initialize();

      const stats = cemProvider.getStats();
      const manifestSources = cemProvider.getAllManifestSources();

      if (stats.manifestCount === 0) {
        Logger.log('  ❌ No custom-elements.json files found');
        Logger.log('    💡 Make sure your workspace contains web component projects');
        Logger.log('    💡 Check that components are built and manifests are generated');
      } else {
        Logger.log(`  Found ${stats.manifestCount} manifest file(s):`);

        for (const [manifestPath, sources] of manifestSources) {
          const relativePath = path.relative(workspaceDir, manifestPath);
          try {
            const content = await fs.readFile(manifestPath, 'utf-8');
            const data = JSON.parse(content);
            const moduleCount = data.modules?.length || 0;

            // Show source information
            const source = sources[0]; // Take the first source
            const sourceInfo = source.isLocal
              ? 'local'
              : source.dependencyName
                ? `dependency: ${source.dependencyName}`
                : 'dependency';

            Logger.log(`    ✅ ${relativePath} (${moduleCount} modules) [${sourceInfo}]`);
          } catch {
            Logger.log(`    ⚠️  ${relativePath} (invalid JSON)`);
          }
        }
      }
    } catch (error) {
      Logger.log('  ❌ Error scanning for manifests');
      if (options.verbose) {
        Logger.error('Manifest scan error:', error);
      }
    }

    Logger.log('');
  }

  private async showComponentInfo(workspaceDir: string, options: InfoOptions): Promise<void> {
    Logger.log('🧩 Web Components:');

    try {
      const cemProvider = new CemProvider(workspaceDir);
      await cemProvider.initialize();

      const components = await cemProvider.getAllComponents('basic');

      if (components.length === 0) {
        Logger.log('  ❌ No web components found');
        Logger.log('    💡 Ensure custom-elements.json files contain component definitions');
      } else {
        Logger.log(`  Found ${components.length} component(s):`);

        // Group by package/source
        const bySource = new Map<string, any[]>();

        for (const component of components.slice(0, 20)) {
          // Limit to first 20
          const source = component.source || 'workspace';
          if (!bySource.has(source)) {
            bySource.set(source, []);
          }
          bySource.get(source)!.push(component);
        }

        for (const [source, sourceComponents] of bySource) {
          Logger.log(`    📦 ${source}:`);
          for (const component of sourceComponents) {
            const tag = component.tagName || component.name;
            const description = component.summary || component.description || '';
            const truncatedDesc = description.length > 50 ? description.substring(0, 47) + '...' : description;
            Logger.log(`      - ${tag}${truncatedDesc ? ` - ${truncatedDesc}` : ''}`);
          }
        }

        if (components.length > 20) {
          Logger.log(`    ... and ${components.length - 20} more`);
        }
      }
    } catch (error) {
      Logger.log('  ❌ Error loading components');
      if (options.verbose) {
        Logger.error('Component loading error:', error);
      }
    }

    Logger.log('');
  }

  private async findManifestFiles(workspaceDir: string): Promise<string[]> {
    try {
      const { glob } = await import('glob');
      const patterns = ['**/custom-elements.json', '**/node_modules/**/custom-elements.json'];

      const files: string[] = [];
      for (const pattern of patterns) {
        const matches = await glob(pattern, {
          cwd: workspaceDir,
          absolute: true,
          ignore: ['**/node_modules/**/node_modules/**'],
        });
        files.push(...matches);
      }

      return [...new Set(files)]; // Remove duplicates
    } catch {
      return [];
    }
  }
}
