import * as fs from 'fs/promises';
import * as path from 'path';
import { createInterface } from 'readline';
import type { CliOptions } from '@wcai/shared';
import { Logger } from '../system/logger';
import { ConfigLoader, type Config } from '../config/config-loader';

interface InitOptions extends CliOptions {
  force?: boolean;
  template?: 'basic' | 'advanced' | 'daemon';
}

export class InitCommand {
  private rl = createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  async execute(options: InitOptions): Promise<void> {
    try {
      Logger.configure(options.verbose ? 'debug' : 'info');
      Logger.log('🚀 Welcome to Web Component AI Tools CLI setup!');

      const workspaceDir = options.workspace || process.cwd();
      const configPath = path.join(workspaceDir, '.wcairc.json');

      // Check if config already exists
      if (!options.force) {
        try {
          await fs.access(configPath);
          const overwrite = await this.ask('Configuration file already exists. Overwrite? (y/N): ');
          if (!overwrite.toLowerCase().startsWith('y')) {
            Logger.log('Setup cancelled.');
            return;
          }
        } catch {
          // Config doesn't exist, continue
        }
      }

      let config: Config;

      if (options.template) {
        config = this.getTemplateConfig(options.template);
        Logger.log(`Using ${options.template} template configuration.`);
      } else {
        config = await this.interactiveSetup(workspaceDir);
      }

      // Write config file
      await fs.writeFile(configPath, JSON.stringify(config, null, 2));
      Logger.log(`✅ Configuration saved to ${configPath}`);

      // Provide next steps
      this.showNextSteps(config);

    } catch (error) {
      Logger.error('Failed to initialize configuration', error);
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  private async interactiveSetup(workspaceDir: string): Promise<Config> {
    Logger.log('\n📋 Let\'s set up your configuration...\n');

    // Detect existing manifests
    const manifestsFound = await this.detectManifests(workspaceDir);
    if (manifestsFound.length > 0) {
      Logger.log(`🔍 Found ${manifestsFound.length} custom-elements.json files:`);
      manifestsFound.forEach(manifest => Logger.log(`  - ${manifest}`));
    }

    // Basic setup questions
    const workspace = await this.ask(`Workspace directory (${workspaceDir}): `) || workspaceDir;
    
    const transport = await this.askChoice(
      'Preferred transport method:',
      ['auto', 'stdio', 'http'],
      'auto'
    );

    let config: Partial<Config> = {
      workspace: workspace === workspaceDir ? undefined : workspace,
      transport: transport as any,
    };

    // HTTP-specific config
    if (transport === 'http' || transport === 'auto') {
      const port = await this.ask('HTTP port (0 for auto): ');
      const host = await this.ask('HTTP host (127.0.0.1): ') || '127.0.0.1';
      
      config.http = {
        port: port ? parseInt(port) : 0,
        host,
      };
    }

    // Logging config
    const logLevel = await this.askChoice(
      'Log level:',
      ['error', 'warn', 'info', 'debug'],
      'warn'
    );

    config.logging = {
      level: logLevel as any,
    };

    // Advanced options
    const advanced = await this.ask('Configure advanced options? (y/N): ');
    if (advanced.toLowerCase().startsWith('y')) {
      config = await this.advancedSetup(config);
    }

    const configLoader = new ConfigLoader();
    return configLoader.generateExampleConfig();
  }

  private async advancedSetup(config: Partial<Config>): Promise<Partial<Config>> {
    Logger.log('\n⚙️  Advanced Configuration:\n');

    // Manifest search paths
    const customPaths = await this.ask('Custom manifest search paths (comma-separated): ');
    if (customPaths) {
      config.manifests = {
        searchPaths: customPaths.split(',').map(p => p.trim()),
      };
    }

    // Daemon config
    const configureDaemon = await this.ask('Configure daemon settings? (y/N): ');
    if (configureDaemon.toLowerCase().startsWith('y')) {
      const pidFile = await this.ask('Custom PID file path (optional): ');
      const logFile = await this.ask('Custom log file path (optional): ');
      
      if (pidFile || logFile) {
        config.daemon = {
          pidFile: pidFile || undefined,
          logFile: logFile || undefined,
        };
      }
    }

    return config;
  }

  private getTemplateConfig(template: string): Config {
    const configLoader = new ConfigLoader();
    const baseConfig = configLoader.generateExampleConfig();

    switch (template) {
      case 'basic':
        return {
          transport: 'auto',
          logging: { level: 'warn' },
        };

      case 'advanced':
        return baseConfig;

      case 'daemon':
        return {
          ...baseConfig,
          transport: 'http',
          http: { port: 3000, host: '127.0.0.1' },
          daemon: {
            pidFile: '/tmp/wcai-mcp.pid',
            logFile: '/tmp/wcai-mcp.log',
          },
          logging: { level: 'info' },
        };

      default:
        return baseConfig;
    }
  }

  private async detectManifests(workspaceDir: string): Promise<string[]> {
    try {
      const { glob } = await import('glob');
      return await glob('**/custom-elements.json', {
        cwd: workspaceDir,
        ignore: ['**/node_modules/**'],
      });
    } catch {
      return [];
    }
  }

  private async ask(question: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  private async askChoice(question: string, choices: string[], defaultChoice: string): Promise<string> {
    const choiceText = choices.map((c, i) => `${i + 1}) ${c}`).join('\n  ');
    const defaultIndex = choices.indexOf(defaultChoice) + 1;
    
    const answer = await this.ask(`${question}\n  ${choiceText}\nChoice (${defaultIndex}): `);
    
    if (!answer) return defaultChoice;
    
    const index = parseInt(answer) - 1;
    if (index >= 0 && index < choices.length) {
      return choices[index];
    }
    
    return defaultChoice;
  }

  private showNextSteps(config: Config): void {
    Logger.log('\n🎉 Setup complete! Next steps:\n');
    
    Logger.log('1. Test your configuration:');
    if (config.transport === 'stdio' || config.transport === 'auto') {
      Logger.log('   wcai-mcp stdio');
    }
    if (config.transport === 'http' || config.transport === 'auto') {
      Logger.log(`   wcai-mcp serve${config.http?.port ? ` --port ${config.http.port}` : ''}`);
    }

    Logger.log('\n2. Start as daemon:');
    Logger.log('   wcai-mcp daemon');

    Logger.log('\n3. Check status:');
    Logger.log('   wcai-mcp status');

    Logger.log('\n4. Configure your AI assistant:');
    Logger.log('   See: https://github.com/d13/vscode-web-components-ai/blob/main/docs/configure-mcp.md');

    Logger.log('\n📝 Edit .wcairc.json to customize further.');
  }
}
