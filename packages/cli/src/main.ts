#!/usr/bin/env node

import { Command } from 'commander';
import { StdioCommand } from './commands/stdio';
import { ServeCommand } from './commands/serve';
import { DaemonCommand } from './commands/daemon';
import { StatusCommand } from './commands/status';
import { InitCommand } from './commands/init';
import { InfoCommand } from './commands/info';
import { DevCommand } from './commands/dev';

const program = new Command();

program.name('wcai-mcp').description('Web Component AI Tools - MCP Server CLI').version('0.0.3');

// STDIO transport command (primary)
program
  .command('stdio')
  .description('Start MCP server with STDIO transport')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-t, --timeout <ms>', 'Request timeout in milliseconds', '5000')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new StdioCommand();
    await command.execute(options);
  });

// HTTP server command
program
  .command('serve')
  .description('Start MCP server with HTTP/SSE transport')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-p, --port <number>', 'HTTP port number', '0')
  .option('-h, --host <address>', 'HTTP host address', '127.0.0.1')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new ServeCommand();
    await command.execute(options);
  });

// Daemon management commands
program
  .command('daemon')
  .description('Start MCP server as background daemon')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-p, --port <number>', 'HTTP port number', '0')
  .option('-h, --host <address>', 'HTTP host address', '127.0.0.1')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new DaemonCommand();
    await command.execute(options);
  });

program
  .command('start')
  .description('Start daemon (alias for daemon command)')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-p, --port <number>', 'HTTP port number', '0')
  .option('-h, --host <address>', 'HTTP host address', '127.0.0.1')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new DaemonCommand();
    await command.execute(options);
  });

program
  .command('stop')
  .description('Stop running daemon')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .action(async options => {
    const command = new StatusCommand();
    await command.stop(options);
  });

program
  .command('status')
  .description('Check daemon status')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .action(async options => {
    const command = new StatusCommand();
    await command.execute(options);
  });

// Initialize configuration
program
  .command('init')
  .description('Initialize configuration file')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-f, --force', 'Overwrite existing configuration')
  .option('-t, --template <type>', 'Use configuration template', 'basic')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new InitCommand();
    await command.execute(options);
  });

// System information
program
  .command('info')
  .description('Show system and project information')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-c, --config', 'Show configuration information')
  .option('-m, --manifests', 'Show manifest information')
  .option('-p, --components', 'Show component information')
  .option('-a, --all', 'Show all information')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new InfoCommand();
    await command.execute(options);
  });

// Development server
program
  .command('dev')
  .description('Start development server with file watching')
  .option('-w, --workspace <path>', 'Workspace directory path', process.cwd())
  .option('-p, --port <number>', 'HTTP port number', '3000')
  .option('-h, --host <address>', 'HTTP host address', '127.0.0.1')
  .option('--watch <paths...>', 'Additional paths to watch')
  .option('--debounce <ms>', 'File change debounce time in milliseconds', '1000')
  .option('-v, --verbose', 'Enable verbose logging')
  .action(async options => {
    const command = new DevCommand();
    await command.execute(options);
  });

// Parse command line arguments
program.parse();
