import * as fs from 'fs/promises';
import * as path from 'path';
import { z } from 'zod';

// Configuration schema
const ConfigSchema = z.object({
  workspace: z.string().optional(),
  transport: z.enum(['stdio', 'http', 'auto']).default('auto'),
  http: z.object({
    port: z.number().min(0).max(65535).default(0),
    host: z.string().default('127.0.0.1'),
  }).optional(),
  stdio: z.object({
    timeout: z.number().min(1000).default(5000),
  }).optional(),
  daemon: z.object({
    pidFile: z.string().optional(),
    logFile: z.string().optional(),
  }).optional(),
  logging: z.object({
    level: z.enum(['error', 'warn', 'info', 'debug']).default('warn'),
    file: z.string().optional(),
  }).optional(),
  manifests: z.object({
    include: z.array(z.string()).optional(),
    exclude: z.array(z.string()).optional(),
    searchPaths: z.array(z.string()).optional(),
  }).optional(),
});

export type Config = z.infer<typeof ConfigSchema>;

export class ConfigLoader {
  private static readonly CONFIG_FILES = [
    '.wcairc.json',
    '.wcairc.js',
    'wcai.config.json',
    'wcai.config.js',
  ];

  async loadConfig(workspaceDir: string = process.cwd()): Promise<Config> {
    // Try to find config file
    const configFile = await this.findConfigFile(workspaceDir);
    let fileConfig: Partial<Config> = {};

    if (configFile) {
      fileConfig = await this.loadConfigFile(configFile);
    }

    // Also check package.json for wcai field
    const packageConfig = await this.loadPackageJsonConfig(workspaceDir);

    // Merge configs (package.json < config file < CLI args)
    const mergedConfig = {
      ...packageConfig,
      ...fileConfig,
    };

    // Validate and return
    return ConfigSchema.parse(mergedConfig);
  }

  private async findConfigFile(workspaceDir: string): Promise<string | null> {
    for (const filename of ConfigLoader.CONFIG_FILES) {
      const filepath = path.join(workspaceDir, filename);
      try {
        await fs.access(filepath);
        return filepath;
      } catch {
        // File doesn't exist, continue
      }
    }
    return null;
  }

  private async loadConfigFile(filepath: string): Promise<Partial<Config>> {
    const ext = path.extname(filepath);
    
    if (ext === '.json') {
      const content = await fs.readFile(filepath, 'utf-8');
      return JSON.parse(content);
    } else if (ext === '.js') {
      // Dynamic import for .js config files
      const configModule = await import(filepath);
      return configModule.default || configModule;
    }
    
    return {};
  }

  private async loadPackageJsonConfig(workspaceDir: string): Promise<Partial<Config>> {
    try {
      const packagePath = path.join(workspaceDir, 'package.json');
      const content = await fs.readFile(packagePath, 'utf-8');
      const packageJson = JSON.parse(content);
      return packageJson.wcai || {};
    } catch {
      return {};
    }
  }

  // Generate example config file
  generateExampleConfig(): Config {
    return {
      workspace: './src',
      transport: 'auto',
      http: {
        port: 3000,
        host: '127.0.0.1',
      },
      stdio: {
        timeout: 5000,
      },
      daemon: {
        pidFile: '/tmp/wcai-mcp.pid',
        logFile: '/tmp/wcai-mcp.log',
      },
      logging: {
        level: 'info',
        file: './wcai.log',
      },
      manifests: {
        include: ['**/custom-elements.json'],
        exclude: ['**/node_modules/**', '**/dist/**'],
        searchPaths: ['./src', './lib', './components'],
      },
    };
  }
}
