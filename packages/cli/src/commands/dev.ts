import * as fs from 'fs';
import * as path from 'path';
import type { CliOptions } from '@wcai/shared';
import { Logger } from '../system/logger';
import { CemProvider } from '../cem/provider';
import { createHttpTransport } from '../transports/http';
import { setupMcpServer } from '../mcp/setup';
import { ConfigLoader } from '../config/config-loader';

interface DevOptions extends CliOptions {
  port?: number;
  host?: string;
  watch?: string[];
  debounce?: number;
}

export class DevCommand {
  private watchers: fs.FSWatcher[] = [];
  private reloadTimeout: NodeJS.Timeout | null = null;
  private cemProvider: CemProvider | null = null;
  private server: any = null;

  async execute(options: DevOptions): Promise<void> {
    try {
      Logger.configure(options.verbose ? 'debug' : 'info');
      Logger.log('🚀 Starting Web Component AI Tools in development mode...');

      const workspaceDir = options.workspace || process.cwd();

      // Load configuration
      const configLoader = new ConfigLoader();
      const config = await configLoader.loadConfig(workspaceDir);

      // Override with CLI options
      const port = options.port || config.http?.port || 3000;
      const host = options.host || config.http?.host || '127.0.0.1';
      const debounce = options.debounce || 1000;

      Logger.log(`Workspace: ${workspaceDir}`);
      Logger.log(`Server: http://${host}:${port}`);
      Logger.log(`Watch debounce: ${debounce}ms`);

      // Start initial server
      await this.startServer(workspaceDir, host, port);

      // Set up file watching
      await this.setupFileWatching(workspaceDir, options.watch, debounce);

      Logger.log('\n✅ Development server started!');
      Logger.log('📁 Watching for changes to custom-elements.json files...');
      Logger.log('🔄 Server will automatically reload when files change');
      Logger.log('⏹️  Press Ctrl+C to stop\n');

      // Keep process running
      process.on('SIGINT', () => this.shutdown());
      process.on('SIGTERM', () => this.shutdown());

      // Keep alive
      await new Promise(() => {}); // Run forever until killed
    } catch (error) {
      Logger.error('Failed to start development server', error);
      await this.shutdown();
      process.exit(1);
    }
  }

  private async startServer(workspaceDir: string, host: string, port: number): Promise<void> {
    try {
      // Initialize CEM provider
      this.cemProvider = new CemProvider(workspaceDir);
      await this.cemProvider.initialize();

      // Create HTTP transport
      const transport = await createHttpTransport(port, host, mcpServer => {
        setupMcpServer(mcpServer, this.cemProvider!);
      });

      this.server = transport.httpServer;

      Logger.log(`🌐 Server running at http://${host}:${port}`);

      // Log component count
      const components = await this.cemProvider.getAllComponents('basic');
      Logger.log(`📦 Loaded ${components.length} components`);
    } catch (error) {
      Logger.error('Failed to start server', error);
      throw error;
    }
  }

  private async setupFileWatching(
    workspaceDir: string,
    customPaths?: string[],
    debounce: number = 1000,
  ): Promise<void> {
    const watchPaths = customPaths || [
      path.join(workspaceDir, '**/custom-elements.json'),
      path.join(workspaceDir, 'package.json'),
      path.join(workspaceDir, '.wcairc.json'),
    ];

    Logger.log(`👀 Watching paths: ${watchPaths.join(', ')}`);

    for (const watchPath of watchPaths) {
      try {
        // Use glob to find actual files to watch
        const { glob } = await import('glob');
        const files = await glob(watchPath, {
          ignore: ['**/node_modules/**'],
        });

        for (const file of files) {
          this.watchFile(file, debounce);
        }

        // Also watch directories for new files
        const dir = path.dirname(watchPath);
        if (fs.existsSync(dir)) {
          this.watchDirectory(dir, debounce);
        }
      } catch (error) {
        Logger.warn(`Failed to watch ${watchPath}:`, error);
      }
    }
  }

  private watchFile(filePath: string, debounce: number): void {
    try {
      const watcher = fs.watch(filePath, eventType => {
        if (eventType === 'change') {
          this.scheduleReload(filePath, debounce);
        }
      });

      this.watchers.push(watcher);
      Logger.log(`📁 Watching: ${path.relative(process.cwd(), filePath)}`);
    } catch (error) {
      Logger.warn(`Failed to watch file ${filePath}:`, error);
    }
  }

  private watchDirectory(dirPath: string, debounce: number): void {
    try {
      const watcher = fs.watch(dirPath, { recursive: true }, (_eventType, filename) => {
        if (
          filename &&
          (filename.endsWith('custom-elements.json') ||
            filename.endsWith('package.json') ||
            filename.endsWith('.wcairc.json'))
        ) {
          const fullPath = path.join(dirPath, filename);
          this.scheduleReload(fullPath, debounce);
        }
      });

      this.watchers.push(watcher);
    } catch (error) {
      Logger.warn(`Failed to watch directory ${dirPath}:`, error);
    }
  }

  private scheduleReload(changedFile: string, debounce: number): void {
    const relativePath = path.relative(process.cwd(), changedFile);
    Logger.log(`📝 File changed: ${relativePath}`);

    // Clear existing timeout
    if (this.reloadTimeout) {
      clearTimeout(this.reloadTimeout);
    }

    // Schedule reload with debounce
    this.reloadTimeout = setTimeout(async () => {
      await this.reloadServer(changedFile);
    }, debounce);
  }

  private async reloadServer(changedFile: string): Promise<void> {
    try {
      Logger.log('🔄 Reloading server...');

      // Reinitialize CEM provider
      if (this.cemProvider) {
        await this.cemProvider.initialize();

        // Log updated component count
        const components = await this.cemProvider.getAllComponents('basic');
        Logger.log(`📦 Reloaded ${components.length} components`);
      }

      // If config file changed, we might need to restart completely
      if (changedFile.endsWith('.wcairc.json') || changedFile.endsWith('package.json')) {
        Logger.log('⚙️  Configuration changed - consider restarting for full effect');
      }

      Logger.log('✅ Server reloaded successfully');
    } catch (error) {
      Logger.error('❌ Failed to reload server:', error);
    }
  }

  private async shutdown(): Promise<void> {
    Logger.log('\n🛑 Shutting down development server...');

    // Close file watchers
    for (const watcher of this.watchers) {
      try {
        watcher.close();
      } catch (error) {
        Logger.warn('Error closing watcher:', error);
      }
    }

    // Clear reload timeout
    if (this.reloadTimeout) {
      clearTimeout(this.reloadTimeout);
    }

    // Close server
    if (this.server) {
      try {
        this.server.close();
      } catch (error) {
        Logger.warn('Error closing server:', error);
      }
    }

    Logger.log('👋 Development server stopped');
    process.exit(0);
  }
}
